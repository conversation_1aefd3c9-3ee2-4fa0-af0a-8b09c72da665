import { generateUserExperiencePrompt } from "./prompt.js";
import { AiService } from "../../../../../src/ai/ai.service.js";
import { extractJson } from "../../../../../src/scripts/utils/jsonUtils.js";

export class UserExperienceAgent {
  constructor() {
    this.aiService = new AiService();
  }

  /**
   * Generates user experience flow for a feature
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Object} routes - Existing routes
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @returns {Promise<Array>} User experience flow array
   */
  async generate(feature, payment, config, routes, provider, model) {
    try {
      const prompt = generateUserExperiencePrompt(feature, payment, config, routes);
      
      const messages = [{ role: "user", content: prompt }];
      
      const response = await this.aiService.send({
        model,
        provider,
        messages,
        options: {
          ownerId: "userExperience",
          ownerType: `${feature.name}_ux`,
        },
      });

      // Extract the user experience array from the response
      const result = extractJson(response);
      
      // Validate that we got a user experience array
      if (!result || !Array.isArray(result)) {
        throw new Error("Invalid user experience response format");
      }

      return result;
    } catch (error) {
      throw new Error(`UserExperienceAgent failed: ${error.message}`);
    }
  }

  /**
   * Retry mechanism for user experience generation
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Object} routes - Existing routes
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<Array>} User experience flow array
   */
  async generateWithRetry(feature, payment, config, routes, provider, model, maxRetries = 2) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`UserExperienceAgent attempt ${attempt}/${maxRetries} for feature: ${feature.name}`);
        return await this.generate(feature, payment, config, routes, provider, model);
      } catch (error) {
        lastError = error;
        console.warn(`UserExperienceAgent attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    
    throw new Error(`UserExperienceAgent failed after ${maxRetries} attempts: ${lastError.message}`);
  }
}
