import { userExperiencePrompt } from "../../templates/userExperiences.js";

export const generateUserExperiencePrompt = (feature, payment, config, routes) => `
# How to Respond to This Prompt

To get the output structure, execute the **Prompt** provided below.

The output must:

- Use the custom delimiter format and respect the provided schema.
- Start with \`\`\`markdown and end with \`\`\`.
- Be a single valid markdown text with no extraneous text or wrappers.
- Highlight key concepts or important information with **bold** or _italic_.
- Avoid long blocks of text.
- Use **headers** to break down the content.
- Apply correct markdown syntax for visual clarity and consistency.

## Output Schema

\`\`\`markdown
+++User Experience Flow---
[Array of user experience objects following the exact structure defined in the prompt]
===DELIMITER===
\`\`\`

# Prompt

## Role:
User Experience Flow Architect

## Task:
Generate a comprehensive user experience flow for the feature "${feature.name}" that defines all user and app interactions, ensuring complete coverage of the user story: ${feature.userStory}.

1. **User Experience Flow Generation**:
   Generate the complete user experience flow following all specifications and requirements.

   - **Process**:
     - **Step 1**: Analyze the feature requirements, payment restrictions, and configuration
     - **Step 2**: Map out all user interactions from entry to completion
     - **Step 3**: Define corresponding app responses for each user action
     - **Step 4**: Apply payment and access control logic where applicable
     - **Step 5**: Ensure flow completeness from start to finish

   - **Key Elements**:
     - Complete user journey from feature entry to task completion
     - All user actions (click, fill, select, type, upload)
     - All app responses (navigate, redirect, open, close, validate, send, load)
     - Payment and subscription validation flows
     - Access control conditions
     - Notification specifications
     - Request identifiers for data operations

   - **Requirements**:
     - Follow the exact structure and format specified in the detailed instructions
     - Include all mandatory fields: who, action, where, when (when applicable), if (when applicable)
     - Ensure every user action has a corresponding app response
     - Apply payment restrictions correctly based on payment type
     - Use proper path formats and modal specifications
     - Include complete notification objects when required
     - Maintain consistency with existing routes and avoid duplicates

   - **Restrictions**:
     - Never use "hasAccess" checks on list views (paths like "/posts", "/articles")
     - Always set "if: null" explicitly for ALL user experiences on list view paths
     - Do not create redundant paths that already exist in the routing system
     - Do not include upload buttons (use eventId: "upload_media" with click action)
     - Do not add separate requests for uploads (handled automatically)

## Detailed Instructions:

${userExperiencePrompt(payment, config)}

## Additional Information:

- **Feature Name**: ${feature.name}
- **Feature Description**: ${feature.description}
- **User Story**: ${feature.userStory}
- **Main Screen**: 
\`\`\`json
${JSON.stringify(feature.mainScreen)}
\`\`\`
- **KPIs**: 
\`\`\`json
${JSON.stringify(feature.kpis)}
\`\`\`
- **Payment Restrictions**: 
\`\`\`json
${JSON.stringify(feature?.paymentRestrictions) ?? ""}
\`\`\`
- **Payment Entity**: ${payment.paymentEntity}
- **Payment Type**: ${payment.paymentType}
- **Configuration**:
  - Is Chat: ${config.isChat}
  - Is Media: ${config.isMedia}
  - Is Credit Usage: ${config.isCreditUsage}
- **Existing Routes**:
\`\`\`json
${JSON.stringify(routes)}
\`\`\`
`;
