/**
 * Feature Specification Assembler
 * Combines results from all specialized agents into the final JSON format
 */

export class FeatureSpecAssembler {
  /**
   * Assembles the complete feature specification from agent results
   * @param {Object} feature - The original feature object
   * @param {Array} userExperience - User experience flow from UserExperienceAgent
   * @param {Object} dataSchemas - Data schemas from DataSchemaAgent
   * @param {Array} requests - Requests from RequestAgent
   * @param {Object} screenConditionResult - Screens and conditions from ScreenConditionAgent
   * @returns {Object} Complete feature specification in the required JSON format
   */
  static assemble(feature, userExperience, dataSchemas, requests, screenConditionResult) {
    try {
      // Validate inputs
      this.validateInputs(feature, userExperience, dataSchemas, requests, screenConditionResult);

      // Assemble the complete feature specification
      const featureSpec = {
        feature: {
          name: feature.name,
          description: feature.description,
          userExperience: userExperience,
          screens: screenConditionResult.screens,
          conditions: screenConditionResult.conditions,
          dataSchemas: dataSchemas,
          requests: requests
        }
      };

      // Validate the assembled result
      this.validateAssembledResult(featureSpec);

      console.log(`Feature specification assembled successfully for: ${feature.name}`);
      return featureSpec;

    } catch (error) {
      throw new Error(`FeatureSpecAssembler failed: ${error.message}`);
    }
  }

  /**
   * Validates all input parameters
   * @param {Object} feature - The original feature object
   * @param {Array} userExperience - User experience flow
   * @param {Object} dataSchemas - Data schemas
   * @param {Array} requests - Requests
   * @param {Object} screenConditionResult - Screens and conditions
   */
  static validateInputs(feature, userExperience, dataSchemas, requests, screenConditionResult) {
    if (!feature || !feature.name) {
      throw new Error("Invalid feature object - missing name");
    }

    if (!Array.isArray(userExperience)) {
      throw new Error("Invalid userExperience - must be an array");
    }

    if (!dataSchemas || typeof dataSchemas !== 'object') {
      throw new Error("Invalid dataSchemas - must be an object");
    }

    if (!Array.isArray(requests)) {
      throw new Error("Invalid requests - must be an array");
    }

    if (!screenConditionResult || 
        !screenConditionResult.screens || 
        !screenConditionResult.conditions) {
      throw new Error("Invalid screenConditionResult - missing screens or conditions");
    }

    if (typeof screenConditionResult.screens !== 'object') {
      throw new Error("Invalid screens - must be an object");
    }

    if (!Array.isArray(screenConditionResult.conditions)) {
      throw new Error("Invalid conditions - must be an array");
    }
  }

  /**
   * Validates the assembled feature specification
   * @param {Object} featureSpec - The assembled feature specification
   */
  static validateAssembledResult(featureSpec) {
    if (!featureSpec || !featureSpec.feature) {
      throw new Error("Invalid assembled result - missing feature object");
    }

    const feature = featureSpec.feature;
    const requiredFields = ['name', 'description', 'userExperience', 'screens', 'conditions', 'dataSchemas', 'requests'];
    
    for (const field of requiredFields) {
      if (!(field in feature)) {
        throw new Error(`Invalid assembled result - missing required field: ${field}`);
      }
    }

    // Additional validation for array/object types
    if (!Array.isArray(feature.userExperience)) {
      throw new Error("Invalid assembled result - userExperience must be an array");
    }

    if (!Array.isArray(feature.requests)) {
      throw new Error("Invalid assembled result - requests must be an array");
    }

    if (!Array.isArray(feature.conditions)) {
      throw new Error("Invalid assembled result - conditions must be an array");
    }

    if (typeof feature.screens !== 'object') {
      throw new Error("Invalid assembled result - screens must be an object");
    }

    if (typeof feature.dataSchemas !== 'object') {
      throw new Error("Invalid assembled result - dataSchemas must be an object");
    }
  }

  /**
   * Creates a summary of the assembled feature specification
   * @param {Object} featureSpec - The assembled feature specification
   * @returns {Object} Summary statistics
   */
  static createSummary(featureSpec) {
    const feature = featureSpec.feature;
    
    return {
      featureName: feature.name,
      userExperienceSteps: feature.userExperience.length,
      screensCount: Object.keys(feature.screens).length,
      conditionsCount: feature.conditions.length,
      dataSchemaEntities: Object.keys(feature.dataSchemas).length,
      requestsCount: feature.requests.length,
      assembledAt: new Date().toISOString()
    };
  }
}
