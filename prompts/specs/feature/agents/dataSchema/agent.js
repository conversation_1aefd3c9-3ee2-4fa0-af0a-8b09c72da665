import { generateDataSchemaPrompt } from "./prompt.js";
import { AiService } from "../../../../../src/ai/ai.service.js";
import { extractJson } from "../../../../../src/scripts/utils/jsonUtils.js";

export class DataSchemaAgent {
  constructor() {
    this.aiService = new AiService();
  }

  /**
   * Generates data schemas for a feature
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @returns {Promise<Object>} Data schemas object
   */
  async generate(feature, payment, config, userExperience, provider, model) {
    try {
      const prompt = generateDataSchemaPrompt(feature, payment, config, userExperience);
      
      const messages = [{ role: "user", content: prompt }];
      
      const response = await this.aiService.send({
        model,
        provider,
        messages,
        options: {
          ownerId: "dataSchema",
          ownerType: `${feature.name}_schema`,
        },
      });

      // Extract the data schemas object from the response
      const result = extractJson(response);
      
      // Validate that we got a data schemas object
      if (!result || typeof result !== 'object') {
        throw new Error("Invalid data schema response format");
      }

      return result;
    } catch (error) {
      throw new Error(`DataSchemaAgent failed: ${error.message}`);
    }
  }

  /**
   * Retry mechanism for data schema generation
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<Object>} Data schemas object
   */
  async generateWithRetry(feature, payment, config, userExperience, provider, model, maxRetries = 2) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`DataSchemaAgent attempt ${attempt}/${maxRetries} for feature: ${feature.name}`);
        return await this.generate(feature, payment, config, userExperience, provider, model);
      } catch (error) {
        lastError = error;
        console.warn(`DataSchemaAgent attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    
    throw new Error(`DataSchemaAgent failed after ${maxRetries} attempts: ${lastError.message}`);
  }
}
