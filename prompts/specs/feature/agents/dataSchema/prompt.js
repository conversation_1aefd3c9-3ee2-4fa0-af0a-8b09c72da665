import { DataSchemaPrompt } from "../../templates/dataSchemas.js";
import { featureConfig } from "../../templates/config.js";

export const generateDataSchemaPrompt = (feature, payment, config, userExperience) => `
# How to Respond to This Prompt

To get the output structure, execute the **Prompt** provided below.

The output must:

- Use the custom delimiter format and respect the provided schema.
- Start with \`\`\`markdown and end with \`\`\`.
- Be a single valid markdown text with no extraneous text or wrappers.
- Highlight key concepts or important information with **bold** or _italic_.
- Avoid long blocks of text.
- Use **headers** to break down the content.
- Apply correct markdown syntax for visual clarity and consistency.

## Output Schema

\`\`\`markdown
+++Data Schemas---
[Object containing all entity schemas with their complete structure and field definitions]
===DELIMITER===
\`\`\`

# Prompt

## Role:
Data Schema Architect

## Task:
Generate comprehensive data schemas for all entities required by the feature "${feature.name}", ensuring complete data structure definitions that support the user experience flow and business requirements.

1. **Data Schema Generation**:
   Define all data entities and their complete structure based on the user experience flow and feature requirements.

   - **Process**:
     - **Step 1**: Analyze the user experience flow to identify all required entities
     - **Step 2**: Define entity properties (name, description, canBeCommented, usePayment)
     - **Step 3**: Specify all fields with complete type definitions and constraints
     - **Step 4**: Apply payment-specific field requirements
     - **Step 5**: Ensure consistency with media, chat, and workflow requirements
     - **Step 6**: Validate field relationships and dependencies

   - **Key Elements**:
     - Entity names (single, descriptive, lowercase nouns)
     - Complete entity descriptions
     - Payment and comment flags (canBeCommented, usePayment)
     - All required fields with type, required, isUser, and description properties
     - Proper field naming conventions (objectId, objectType for dependencies)
     - Media-specific fields for image/video/audio entities
     - Workflow identifiers for generated resources

   - **Requirements**:
     - Follow exact field structure: type, required, isUser, description
     - Include all mandatory fields for payment entities (id, name, price)
     - Use standardized dependency fields (objectId, objectType)
     - Apply media-specific schemas when applicable
     - Include workflow fields for generated resources
     - Ensure field uniqueness and avoid redundancy
     - Match entities referenced in user experience flow

   - **Restrictions**:
     - Do not include access-related fields in payment entities
     - Do not use specific identifiers like "postId" or "userId"
     - Do not duplicate fields with same purpose (e.g., title and name)
     - Do not include fields not required by the feature functionality
     - Do not redefine existing schema structures unnecessarily

## Detailed Instructions:

${DataSchemaPrompt(featureConfig, payment, config)}

## Additional Information:

- **Feature Name**: ${feature.name}
- **Feature Description**: ${feature.description}
- **User Story**: ${feature.userStory}
- **Payment Entity**: ${payment.paymentEntity}
- **Payment Type**: ${payment.paymentType}
- **Configuration**:
  - Is Chat: ${config.isChat}
  - Is Media: ${config.isMedia}
  - Is Credit Usage: ${config.isCreditUsage}
- **User Experience Flow Context**:
\`\`\`json
${JSON.stringify(userExperience, null, 2)}
\`\`\`
`;
