import { RequestsPrompt } from "../../templates/requests.js";

export const generateRequestPrompt = (feature, payment, config, userExperience, dataSchemas) => `
# How to Respond to This Prompt

To get the output structure, execute the **Prompt** provided below.

The output must:

- Use the custom delimiter format and respect the provided schema.
- Start with \`\`\`markdown and end with \`\`\`.
- Be a single valid markdown text with no extraneous text or wrappers.
- Highlight key concepts or important information with **bold** or _italic_.
- Avoid long blocks of text.
- Use **headers** to break down the content.
- Apply correct markdown syntax for visual clarity and consistency.

## Output Schema

\`\`\`markdown
+++Requests---
[Array of request objects with complete CRUD operations and workflow definitions]
===DELIMITER===
\`\`\`

# Prompt

## Role:
API Request Architect

## Task:
Generate comprehensive API requests for the feature "${feature.name}" that support all data operations required by the user experience flow, ensuring complete CRUD functionality and workflow management.

1. **Request Generation**:
   Define all API requests needed to support the user experience flow and data operations.

   - **Process**:
     - **Step 1**: Analyze user experience flow to identify all data operations
     - **Step 2**: Map each operation to appropriate CRUD request types
     - **Step 3**: Define request parameters and body structures based on data schemas
     - **Step 4**: Configure workflow specifications for generated content
     - **Step 5**: Set up AI generation parameters where applicable
     - **Step 6**: Include notification links for user feedback

   - **Key Elements**:
     - Unique request identifiers matching user experience flow
     - Correct CRUD operation types (Find, Read, Create, Update, Delete)
     - Complete parameter and body definitions
     - Workflow task sequences for generated content
     - AI generation specifications (role, objective, generationType)
     - Data schema references
     - Notification link configurations

   - **Requirements**:
     - Every request must correspond to an action in user experience flow
     - All body fields must exactly match data schema field definitions
     - Include all required fields from data schemas (except isUser: true)
     - Use proper parameter structure for Read/Update/Delete operations
     - Configure complete workflow task dependencies and connections
     - Set appropriate AI generation types for content creation
     - Include notification links when specified in user experience

   - **Restrictions**:
     - Do not create requests not referenced in user experience flow
     - Do not include fields not defined in data schemas
     - Do not use parameters for Find or Create operations
     - Do not include isUser: true fields in request bodies
     - Do not create separate upload requests (handled automatically)
     - Do not create dependent requests between different entities

## Detailed Instructions:

${RequestsPrompt(payment)}

## Additional Information:

- **Feature Name**: ${feature.name}
- **Feature Description**: ${feature.description}
- **Payment Entity**: ${payment.paymentEntity}
- **Payment Type**: ${payment.paymentType}
- **Configuration**:
  - Is Chat: ${config.isChat}
  - Is Media: ${config.isMedia}
  - Is Credit Usage: ${config.isCreditUsage}
- **User Experience Flow Context**:
\`\`\`json
${JSON.stringify(userExperience, null, 2)}
\`\`\`
- **Data Schemas Context**:
\`\`\`json
${JSON.stringify(dataSchemas, null, 2)}
\`\`\`
`;
