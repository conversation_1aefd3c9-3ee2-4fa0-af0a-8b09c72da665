import { generateRequestPrompt } from "./prompt.js";
import { AiService } from "../../../../../src/ai/ai.service.js";
import { extractJson } from "../../../../../src/scripts/utils/jsonUtils.js";

export class RequestAgent {
  constructor() {
    this.aiService = new AiService();
  }

  /**
   * Generates requests for a feature
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {Object} dataSchemas - Data schemas from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @returns {Promise<Array>} Requests array
   */
  async generate(feature, payment, config, userExperience, dataSchemas, provider, model) {
    try {
      const prompt = generateRequestPrompt(feature, payment, config, userExperience, dataSchemas);
      
      const messages = [{ role: "user", content: prompt }];
      
      const response = await this.aiService.send({
        model,
        provider,
        messages,
        options: {
          ownerId: "request",
          ownerType: `${feature.name}_requests`,
        },
      });

      // Extract the requests array from the response
      const result = extractJson(response);
      
      // Validate that we got a requests array
      if (!result || !Array.isArray(result)) {
        throw new Error("Invalid requests response format");
      }

      return result;
    } catch (error) {
      throw new Error(`RequestAgent failed: ${error.message}`);
    }
  }

  /**
   * Retry mechanism for request generation
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {Object} dataSchemas - Data schemas from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<Array>} Requests array
   */
  async generateWithRetry(feature, payment, config, userExperience, dataSchemas, provider, model, maxRetries = 2) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`RequestAgent attempt ${attempt}/${maxRetries} for feature: ${feature.name}`);
        return await this.generate(feature, payment, config, userExperience, dataSchemas, provider, model);
      } catch (error) {
        lastError = error;
        console.warn(`RequestAgent attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    
    throw new Error(`RequestAgent failed after ${maxRetries} attempts: ${lastError.message}`);
  }
}
