# Feature Specification Agents Architecture

## Overview

Cette architecture refactorisée décompose la génération monolithique des spécifications de features en 4 agents spécialisés, permettant une meilleure gestion de la complexité tout en préservant le contexte entre les étapes.

## Architecture des Agents

### 1. UserExperienceAgent
**Responsabilité**: Génère le flux d'expérience utilisateur complet
- **Input**: feature, payment, config, routes
- **Output**: Array d'objets user experience
- **Contenu préservé**: Tout le contenu de `templates/userExperiences.js`

### 2. DataSchemaAgent  
**Responsabilité**: Génère les schémas de données pour toutes les entités
- **Input**: feature, payment, config, userExperience (du step 1)
- **Output**: Object contenant tous les schémas d'entités
- **Contenu préservé**: Tout le contenu de `templates/dataSchemas.js`

### 3. RequestAgent
**Responsabilité**: Génère toutes les requêtes API CRUD
- **Input**: feature, payment, config, userExperience, dataSchemas (des steps 1-2)
- **Output**: Array d'objets request
- **Contenu préservé**: Tout le contenu de `templates/requests.js`

### 4. ScreenConditionAgent
**Responsabilité**: Génère les écrans et conditions
- **Input**: feature, payment, config, userExperience, requests (des steps 1-3)
- **Output**: Object avec screens et conditions
- **Contenu préservé**: Tout le contenu de `templates/screens.js` et `templates/guidelines.js`

## Flux d'Exécution

```
1. UserExperienceAgent → userExperience[]
2. DataSchemaAgent → dataSchemas{} (utilise userExperience)
3. RequestAgent → requests[] (utilise userExperience + dataSchemas)
4. ScreenConditionAgent → {screens{}, conditions[]} (utilise userExperience + requests)
5. FeatureSpecAssembler → JSON final complet
```

## Gestion des Erreurs

Chaque agent implémente un mécanisme de retry avec:
- **Max retries**: 2 tentatives par défaut
- **Backoff**: Délai progressif entre les tentatives
- **Error logging**: Logs détaillés pour le debugging

## Format des Prompts

Tous les prompts suivent le format standardisé avec:
- **How to Respond**: Instructions de format
- **Output Schema**: Structure attendue avec délimiteurs
- **Prompt**: Role, Task, Process, Key Elements, Requirements, Restrictions
- **Additional Information**: Contexte dynamique

## Assemblage Final

La classe `FeatureSpecAssembler` combine tous les résultats:
- **Validation**: Vérification de tous les inputs
- **Assembly**: Combinaison mécanique des résultats
- **Summary**: Statistiques de génération

## Avantages de cette Architecture

1. **Réduction de charge**: Chaque LLM traite une section spécialisée
2. **Préservation du contexte**: Passage des résultats entre agents
3. **Maintien de l'information**: Aucune perte du contenu original
4. **Retry granulaire**: Retry par section plutôt que global
5. **Debugging facilité**: Logs par étape et agent

## Utilisation

Le script principal `featureSpecs.js` orchestre automatiquement tous les agents:

```bash
node src/scripts/featureSpecs/featureSpecs.js <provider> <model>
```

## Structure des Fichiers

```
prompts/specs/feature/agents/
├── README.md
├── index.js                    # Exports de tous les agents
├── assembler.js               # Assemblage final
├── userExperience/
│   ├── agent.js              # UserExperienceAgent
│   └── prompt.js             # Prompt spécialisé
├── dataSchema/
│   ├── agent.js              # DataSchemaAgent  
│   └── prompt.js             # Prompt spécialisé
├── request/
│   ├── agent.js              # RequestAgent
│   └── prompt.js             # Prompt spécialisé
└── screenCondition/
    ├── agent.js              # ScreenConditionAgent
    └── prompt.js             # Prompt spécialisé
```

## Compatibilité

Cette architecture est entièrement compatible avec:
- Le format JSON de sortie existant
- Les templates originaux (préservés intégralement)
- Le système de routing existant
- Les outils et utilitaires existants
