import { generateScreenConditionPrompt } from "./prompt.js";
import { AiService } from "../../../../../src/ai/ai.service.js";
import { extractJson } from "../../../../../src/scripts/utils/jsonUtils.js";

export class ScreenConditionAgent {
  constructor() {
    this.aiService = new AiService();
  }

  /**
   * Generates screens and conditions for a feature
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {Array} requests - Requests from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @returns {Promise<Object>} Object containing screens and conditions
   */
  async generate(feature, payment, config, userExperience, requests, provider, model) {
    try {
      const prompt = generateScreenConditionPrompt(feature, payment, config, userExperience, requests);
      
      const messages = [{ role: "user", content: prompt }];
      
      const response = await this.aiService.send({
        model,
        provider,
        messages,
        options: {
          ownerId: "screenCondition",
          ownerType: `${feature.name}_screens`,
        },
      });

      // Extract the screens and conditions from the response
      const result = extractJson(response);
      
      // Validate that we got screens and conditions
      if (!result || typeof result !== 'object' || !result.screens || !result.conditions) {
        throw new Error("Invalid screen/condition response format");
      }

      return result;
    } catch (error) {
      throw new Error(`ScreenConditionAgent failed: ${error.message}`);
    }
  }

  /**
   * Retry mechanism for screen and condition generation
   * @param {Object} feature - The feature object
   * @param {Object} payment - Payment configuration
   * @param {Object} config - Feature configuration
   * @param {Array} userExperience - User experience flow from previous agent
   * @param {Array} requests - Requests from previous agent
   * @param {string} provider - AI provider
   * @param {string} model - AI model
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<Object>} Object containing screens and conditions
   */
  async generateWithRetry(feature, payment, config, userExperience, requests, provider, model, maxRetries = 2) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`ScreenConditionAgent attempt ${attempt}/${maxRetries} for feature: ${feature.name}`);
        return await this.generate(feature, payment, config, userExperience, requests, provider, model);
      } catch (error) {
        lastError = error;
        console.warn(`ScreenConditionAgent attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    
    throw new Error(`ScreenConditionAgent failed after ${maxRetries} attempts: ${lastError.message}`);
  }
}
