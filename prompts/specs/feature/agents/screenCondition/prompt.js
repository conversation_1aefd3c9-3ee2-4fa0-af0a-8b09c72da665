import { ScreensPrompt } from "../../templates/screens.js";
import { GuidelinesPrompt } from "../../templates/guidelines.js";

export const generateScreenConditionPrompt = (feature, payment, config, userExperience, requests) => `
# How to Respond to This Prompt

To get the output structure, execute the **Prompt** provided below.

The output must:

- Use the custom delimiter format and respect the provided schema.
- Start with \`\`\`markdown and end with \`\`\`.
- Be a single valid markdown text with no extraneous text or wrappers.
- Highlight key concepts or important information with **bold** or _italic_.
- Avoid long blocks of text.
- Use **headers** to break down the content.
- Apply correct markdown syntax for visual clarity and consistency.

## Output Schema

\`\`\`markdown
+++Screens---
[Object mapping screen paths to their descriptions and purposes]
===DELIMITER===
+++Conditions---
[Array of conditions required for the feature to function properly]
===DELIMITER===
\`\`\`

# Prompt

## Role:
Screen and Condition Architect

## Task:
Generate comprehensive screen definitions and feature conditions for "${feature.name}" that ensure proper navigation flow and feature functionality requirements.

1. **Screen Definition**:
   Define all screens and their roles in achieving the feature KPIs and user experience.

   - **Process**:
     - **Step 1**: Extract all unique paths from the user experience flow
     - **Step 2**: Define the purpose and contribution of each screen
     - **Step 3**: Ensure payment restriction compliance for screen access
     - **Step 4**: Validate screen consistency with user experience flow
     - **Step 5**: Apply proper path formatting and naming conventions

   - **Key Elements**:
     - All screen paths referenced in user experience flow
     - Clear descriptions of each screen's purpose and functionality
     - Payment restriction considerations for screen access
     - Proper path formatting for entity-based screens
     - Modal specifications for payment and subscription flows

   - **Requirements**:
     - Every path in user experience flow must be defined in screens
     - Use proper entity path format: [entity]s/:id for individual items
     - Include payment restriction logic for subscription/credit features
     - Ensure screen descriptions align with feature KPIs
     - Maintain consistency with existing routing patterns

   - **Restrictions**:
     - Do not create screens not referenced in user experience flow
     - Do not include subscription or credit purchase screens (handled by /paywall)
     - Do not use special characters or spaces in screen names
     - Do not create redundant screen definitions

2. **Condition Definition**:
   List all conditions required for the feature to function properly.

   - **Process**:
     - **Step 1**: Analyze feature requirements and dependencies
     - **Step 2**: Identify user status and permission requirements
     - **Step 3**: Define payment and subscription prerequisites
     - **Step 4**: Specify technical and business rule conditions
     - **Step 5**: Ensure condition completeness and clarity

   - **Key Elements**:
     - User authentication and authorization requirements
     - Payment and subscription status conditions
     - Technical prerequisites and dependencies
     - Business rule and workflow conditions
     - Data access and permission requirements

   - **Requirements**:
     - Include all necessary conditions for feature operation
     - Specify clear and testable condition statements
     - Cover payment, subscription, and access control requirements
     - Include workflow and generation process conditions
     - Ensure conditions align with user experience flow

   - **Restrictions**:
     - Do not include overly granular or implementation-specific conditions
     - Do not duplicate conditions that are already implied
     - Do not include conditions not relevant to the feature functionality

## Screen Guidelines:
${ScreensPrompt(config, payment)}

## General Guidelines:
${GuidelinesPrompt()}

## Additional Information:

- **Feature Name**: ${feature.name}
- **Feature Description**: ${feature.description}
- **User Story**: ${feature.userStory}
- **Main Screen**: 
\`\`\`json
${JSON.stringify(feature.mainScreen)}
\`\`\`
- **KPIs**: 
\`\`\`json
${JSON.stringify(feature.kpis)}
\`\`\`
- **Payment Restrictions**: 
\`\`\`json
${JSON.stringify(feature?.paymentRestrictions) ?? ""}
\`\`\`
- **Payment Entity**: ${payment.paymentEntity}
- **Payment Type**: ${payment.paymentType}
- **Configuration**:
  - Is Chat: ${config.isChat}
  - Is Media: ${config.isMedia}
  - Is Credit Usage: ${config.isCreditUsage}
- **User Experience Flow Context**:
\`\`\`json
${JSON.stringify(userExperience, null, 2)}
\`\`\`
- **Requests Context**:
\`\`\`json
${JSON.stringify(requests, null, 2)}
\`\`\`
`;
