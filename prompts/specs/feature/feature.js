import { outputFormat } from "./templates/output.js";
import { featureConfig } from "./templates/config.js";
import { exampleOutput } from "./templates/example.js";
import { userExperiencePrompt } from "./templates/userExperiences.js";
import { DataSchemaPrompt } from "./templates/dataSchemas.js";
import { RequestsPrompt } from "./templates/requests.js";
import { ScreensPrompt } from "./templates/screens.js";
import { GuidelinesPrompt } from "./templates/guidelines.js";

export const generateFeatureSpecs = (feature, payment, routes, config) => `
# How to Respond to This Prompt

To generate the output, you will need to execute the [prompt] provided below.
The output must adhere to the custom delimiter format and the schema outlined below.
Ensure that the output begins with \`\`\`json and ends with \`\`\`.

It is critical that you output a single valid JSON object with no extraneous text or wrappers.
Use single quotes or escape double quotes to prevent breaking the JSON.

---

## Output Schema

\`\`\`json
${outputFormat(feature)}
\`\`\`

---

## Output Example

\`\`\`json
${JSON.stringify(exampleOutput, null, 2)}
\`\`\`

---

# Prompt

## Role:
Feature Specification Architect

## Task:
Create a JSON document that provides detailed specifications for a single feature, focusing on all aspects of the user experience flow, screens, and conditions necessary to meet the user story: ${
  feature.userStory
}.

---

## Guidelines:
${GuidelinesPrompt()}

## Instructions:
1. **name**: Provided name of the feature. 
2. **description**: provide description of the feature.
3. **User Experience Flow**:
\`\`\`
${userExperiencePrompt(payment, config)}
\`\`\`
4. **Screens**:
\`\`\`
${ScreensPrompt(config, payment)}
\`\`\`
5. **Conditions**: List the conditions required for the feature to function, such as user status, permissions, or other prerequisites.
6. **dataSchemas**:
\`\`\`
${DataSchemaPrompt(featureConfig, payment, config)}
\`\`\`
7. **Requests**:
\`\`\`
${RequestsPrompt(payment)}}
\`\`\`

## [Additional Information]:
- **Main Screen**: The primary screen where the user starts their interaction with the feature.
\`\`\`json
${JSON.stringify(feature.mainScreen)}
\`\`\`
- **KPIs**: Key performance indicators to measure the success of the feature.
\`\`\`json
${JSON.stringify(feature.kpis)}
\`\`\`
- **Payment Restriction**: Indicates if this feature is affected by a payment model and by which type of payment.
\`\`\`json
${JSON.stringify(feature?.paymentRestrictions) ?? ""}
\`\`\`
- **payment**: To indicate the entity that needs to be updated:
\`\`\`
payment:${payment.paymentEntity}
\`\`\`
- **paymentType**: To indicate the payment type:
\`\`\`
paymentType:${payment.paymentType}
\`\`\`
- **Existing Routes**:
\`\`\`
Existing Routes:${JSON.stringify(routes)}
\`\`\`
`;
