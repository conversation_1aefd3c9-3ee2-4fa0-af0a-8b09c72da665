{"purpose": {"required": true, "description": "Briefly describe the primary purpose of the project."}, "targetMarket": {"required": true, "description": "Identify the target audience or market for the MVP."}, "interactionType": {"required": true, "type": "array", "allowedValues": ["chatbot", "media"], "description": "Specifies the type of interaction for the project.", "rules": {"chatbot": "Use when the project includes an AI agent or chatbot interface.", "media": "Use when the project involves generating, processing, or managing images or videos."}}, "userPainPoints": {"required": true, "type": "array", "maxItems": 3, "itemSchema": {"name": {"required": true}, "description": {"required": true}}, "description": "Critical barriers preventing users from achieving the project's core purpose."}, "features": {"required": true, "type": "array", "maxItems": 4, "description": "Defines the must-have features of the <PERSON>.", "rules": {"nonPaymentFeature": {"mustInclude": ["userStory", "kpis", "mainScreen"], "note": "Feature names should be verbs (e.g., 'Create Post', not 'Post Manager')."}, "paymentFeature": {"mustInclude": ["creditType", "paymentType", "creditNeeded", "paymentEntity", "options", "paywallModal"], "note": "If paymentType is 'credit', creditType must be 'payment' or 'usage'."}}, "itemSchema": {"name": {"required": true}, "description": {"required": true}, "userStory": {"requiredIf": "nonPaymentFeature"}, "kpis": {"requiredIf": "nonPaymentFeature", "type": "array", "itemSchema": {"name": {"required": true}, "description": {"required": true}}}, "mainScreen": {"requiredIf": "nonPaymentFeature", "itemSchema": {"name": {"required": true}, "description": {"required": true}}}, "paymentRestrictions": {"required": true, "description": "Specifies access restrictions (subscription, one-time-payment, credit, cart) or null if free.", "rules": {"null": "Set to null if no restriction access on that resource.", "subscription": "User cannot access any information about the resource before subscription.", "credit_or_one-time-payment_or_cart": "Users can view basic info (e.g., name, thumbnail) but full access requires purchase."}}, "creditType": {"requiredIf": "paymentFeature"}, "paymentType": {"requiredIf": "paymentFeature"}, "creditNeeded": {"requiredIf": "paymentFeature"}, "paymentEntity": {"requiredIf": "paymentFeature"}, "options": {"requiredIf": "paymentFeature"}, "paywallModal": {"requiredIf": "paymentFeature"}}}, "authenticationMethods": {"required": true, "type": "object", "minProperties": 2, "mustInclude": ["email_password"], "allowedKeys": ["email_password", "google", "facebook", "twitter", "linkedin"], "description": "Available authentication options for login.", "rules": {"email_password": "This method is always required.", "minimumMethods": "At least one additional method (e.g., google, facebook, twitter, linkedin) must be included."}}}