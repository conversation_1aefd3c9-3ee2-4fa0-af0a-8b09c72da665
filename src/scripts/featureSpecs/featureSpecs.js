// Import required modules and configurations
import "dotenv/config";
import path from "path";
import { extractJson } from "../utils/jsonUtils.js";
import { removeWhiteSpace, replaceText } from "../utils/stringUtils.js";
import { AiService } from "../../ai/ai.service.js";
import { generateRouteSyncSpecs } from "../../../prompts/index.js";
import { FileHandler } from "../utils/FileHandler.js";
import { pathManager } from "../utils/pathManager.js";
import { DEFAULT_AI_MODEL, DEFAULT_AI_PROVIDER } from "../utils/constant.js";
import { ApplicationError } from "../utils/CustomError.js";
import {
  UserExperienceAgent,
  DataSchemaAgent,
  RequestAgent,
  ScreenConditionAgent,
  FeatureSpecAssembler,
} from "../../../prompts/specs/feature/agents/index.js";

// Initialize AI service and file handler
const aiService = new AiService();
const fileHandler = new FileHandler();

/**
 * Generates routing data from a feature's pages.
 * @param {Object} feature - The feature object containing page data.
 * @returns {Object} - An object representing routing data for each page.
 */
function routingObject(feature) {
  // Reduce the feature's screens to a routing data object
  const routingData = Object.entries(feature.screens).reduce(
    (acc, [path, description]) => {
      // If the description and path exist, add the routing data to the accumulator
      if (description && path) {
        acc[path] = {
          description,
          useIn: [removeWhiteSpace(feature.name)],
        };
      }
      return acc;
    },
    {}
  );
  return routingData;
}

/**
 * Creates a routing application file.
 * @param {Object} feature - The feature object containing page data.
 */
async function createRoutingAppFile(feature) {
  // Check if feature.screens exists and has pages
  if (!feature.screens || Object.keys(feature.screens).length === 0) {
    throw new ApplicationError(
      `No pages found in the feature to create routingApp.json`,
      { featureName: feature.name }
    );
  }

  // Compile routing data from all pages in the feature
  const routingAppData = routingObject(feature);

  // Delete specific routes
  const existingScreen = ["/paywall", "/workflow/:id", "/chatbot"];

  existingScreen.forEach((screen) => {
    if (routingAppData[screen]) {
      delete routingAppData[screen];
    }
  });

  // Write the routing data to the routing application file
  await fileHandler.writeJson(pathManager.output.routingApp, routingAppData);
  return routingAppData;
}

/**
 * Updates routing files based on AI-generated changes.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 * @param {string} specPath - The path to the feature specification file.
 * @param {string} featId - The ID of the feature.
 */
async function updateRoutingFiles(provider, model, specPath, featId) {
  try {
    // Read the current routingApp data
    const routingApp = await fileHandler.readJson(
      pathManager.output.routingApp
    );
    // Read the feature specifications
    let featureSpecs = await fileHandler.readJson(specPath);

    // Extract the feature from the specifications
    let feature = featureSpecs?.feature ?? {};
    // Get the paths of the screens from the feature
    let paths = feature?.screens ? Object.keys(feature.screens) : {};

    // If there are paths, proceed with updating the routing files
    if (Object.keys(paths).length > 0) {
      // Generate synchronization prompt for routingApp and feature specs
      const syncPrompt = generateRouteSyncSpecs(
        JSON.stringify(routingObject(feature)),
        JSON.stringify(routingApp)
      );

      // Send synchronization prompt to AI and get response
      const messages = [{ role: "user", content: syncPrompt }];
      const response = await aiService.send({
        model,
        provider,
        messages,
        options: {
          ownerId: "feature",
          ownerType: `sync_${featId}`,
        },
      });
      const changes = extractJson(response);

      // If there are changes and path changes, update the feature pages with new paths
      if (
        Object.keys(changes).length > 0 &&
        Object.keys(changes.pathChanges).length > 0
      ) {
        paths.forEach(async (path) => {
          // Check if the page's current path matches any oldPath in pathChanges
          if (changes.pathChanges[path]) {
            const newPath = changes.pathChanges[path]["newPath"];

            const stringFeature = JSON.stringify(feature);

            // Update feature pages with new paths
            feature = JSON.parse(replaceText(stringFeature, path, newPath));
            await fileHandler.writeJson(specPath, { feature });
          }
        });
      }

      // If the routing application has keys
      if (Object.keys(routingApp).length > 0) {
        const featKey = removeWhiteSpace(feature.name);
        // Update `paths` with the latest page names after potential path changes
        paths = Object.keys(feature.screens);

        paths.forEach((path) => {
          const { pathChanges } = changes;
          const existingPage = routingApp[path];
          const hasChanges =
            Object.keys(changes).length > 0 && pathChanges[path];

          const description = hasChanges
            ? pathChanges[path]["newDescription"]
            : feature.screens[path];

          const newPath = hasChanges ? pathChanges[path]["newPath"] : path;

          if (!existingPage) {
            // Add new page to routingApp
            const newPage = {
              description,
              useIn: [featKey],
            };
            routingApp[path] = newPage;
          } else {
            // Update the useIn array to include the current feature
            if (!existingPage.useIn.includes(featKey)) {
              existingPage.description =
                existingPage.description + feature.screens[newPath];
              existingPage.useIn.push(featKey);
            }
          }
        });
      }
      // Delete specific routes
      delete routingApp["/paywall"];
      delete routingApp["/workflow/:id"];

      // Write updated routingApp and feature specs to files
      await fileHandler.writeJson(pathManager.output.routingApp, routingApp);

      console.log(
        "RoutingApp and RoutingSpec updated successfully for:",
        feature.name
      );
      return routingApp;
    }
  } catch (error) {
    throw new ApplicationError(
      `updateRoutingFiles failed for feature id "${featId}": ${error.message}`,
      { originalError: error }
    );
  }
}

/**
 * Processes a single feature using specialized agents
 * @param {Object} feature - The feature object
 * @param {Object} paymentFeature - Payment configuration
 * @param {Object} config - Feature configuration
 * @param {Object} routes - Existing routes
 * @param {string} provider - AI provider
 * @param {string} model - AI model
 * @returns {Promise<Object>} Complete feature specification
 */
async function processFeatureWithAgents(
  feature,
  paymentFeature,
  config,
  routes,
  provider,
  model
) {
  try {
    console.log(`Processing feature with agents: ${feature.name}`);

    // Initialize agents
    const userExperienceAgent = new UserExperienceAgent();
    const dataSchemaAgent = new DataSchemaAgent();
    const requestAgent = new RequestAgent();
    const screenConditionAgent = new ScreenConditionAgent();

    // Step 1: Generate User Experience Flow
    console.log(`Step 1: Generating user experience for ${feature.name}`);
    const userExperience = await userExperienceAgent.generateWithRetry(
      feature,
      paymentFeature,
      config,
      routes,
      provider,
      model
    );

    // Step 2: Generate Data Schemas
    console.log(`Step 2: Generating data schemas for ${feature.name}`);
    const dataSchemas = await dataSchemaAgent.generateWithRetry(
      feature,
      paymentFeature,
      config,
      userExperience,
      provider,
      model
    );

    // Step 3: Generate Requests
    console.log(`Step 3: Generating requests for ${feature.name}`);
    const requests = await requestAgent.generateWithRetry(
      feature,
      paymentFeature,
      config,
      userExperience,
      dataSchemas,
      provider,
      model
    );

    // Step 4: Generate Screens and Conditions
    console.log(
      `Step 4: Generating screens and conditions for ${feature.name}`
    );
    const screenConditionResult = await screenConditionAgent.generateWithRetry(
      feature,
      paymentFeature,
      config,
      userExperience,
      requests,
      provider,
      model
    );

    // Step 5: Assemble final result
    console.log(`Step 5: Assembling final specification for ${feature.name}`);
    const featureSpec = FeatureSpecAssembler.assemble(
      feature,
      userExperience,
      dataSchemas,
      requests,
      screenConditionResult
    );

    // Log summary
    const summary = FeatureSpecAssembler.createSummary(featureSpec);
    console.log(`Feature ${feature.name} processed successfully:`, summary);

    return featureSpec;
  } catch (error) {
    throw new ApplicationError(
      `Error processing feature ${feature.name} with agents: ${error.message}`,
      {
        originalError: error,
        featureName: feature.name,
      }
    );
  }
}

/**
 * Processes all features by using specialized agents and saving responses.
 * @param {string} provider - The AI provider to use.
 * @param {string} model - The AI model to use.
 */
async function processFeatures(provider, model) {
  try {
    // Read the specifications JSON file
    const projectSpecs = await fileHandler.readJson(
      pathManager.output.specificationsApp
    );
    // Extract the features from the projectSpecs
    const features = projectSpecs?.features ?? [];

    // Return early if no features are found
    if (features.length === 0) {
      throw new ApplicationError(`No features found in the specifications.`, {
        function: "processFeatures",
      });
    }
    // Find the payment feature
    const paymentFeature = features.find(
      (feature) => feature.paymentEntity
    ) ?? { paymentEntity: "", paymentType: "" };

    const isCreditUsage = !!features.find(
      (feature) => feature.creditType === "usage"
    );

    const config = {
      isChat: !!projectSpecs.interactionType.includes("chatbot"),
      isMedia: !!projectSpecs.interactionType.includes("media"),
      isCreditUsage,
    };

    // Process each feature in the specifications
    for (let index = 0; index < features.length; index++) {
      const feature = features[index];
      // If the feature has a user story, process it
      if (feature?.userStory) {
        let routes = {};

        const featId = removeWhiteSpace(features[index].name);

        // Use specialized agents to process the feature
        const response = await processFeatureWithAgents(
          feature,
          paymentFeature,
          config,
          routes,
          provider,
          model
        );

        const outputFilePath = path.join(
          pathManager.output.features,
          `${featId}.json`
        ); // Path for output file

        await fileHandler.writeJson(outputFilePath, response);

        // Create routingApp.json for the first feature
        if (index === 0 && response && response.feature) {
          routes = await createRoutingAppFile(response.feature);
        } else {
          // Update files for subsequent features
          routes = await updateRoutingFiles(
            provider,
            model,
            outputFilePath,
            featId
          );
        }
      }
    }
  } catch (error) {
    throw new ApplicationError(`Error in processFeatures: ${error.message}`, {
      originalError: error,
    });
  }
}

/**
 * Entry point: Validates arguments and initiates feature processing.
 */
(async () => {
  // Extract provider and model from command line arguments
  const [provider = DEFAULT_AI_PROVIDER, model = DEFAULT_AI_MODEL] =
    process.argv.slice(2);
  // If provider or model is missing, log an error and exit
  if (!provider || !model) {
    console.error(
      "Error: Missing provider or model. Usage: node script.js <provider> <model>"
    );
    process.exit(1);
  }
  await fileHandler.deleteFolder(pathManager.output.features);
  await processFeatures(provider, model);
})();
