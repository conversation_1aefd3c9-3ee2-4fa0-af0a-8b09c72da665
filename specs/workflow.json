{"workflow": [{"step": 1, "script": "project", "status": "completed", "description": "Generate project configuration", "test": "completed"}, {"step": 2, "script": "feature", "status": "completed", "description": "Generate feature configuration"}, {"step": 3, "script": "screen", "status": "completed", "description": "Generate screen specifications and unify feature data"}, {"step": 4, "script": "step", "status": "completed", "description": "Generate workflow steps and task configurations"}, {"step": 5, "script": "navigation", "status": "completed", "description": "Generate navigation configuration and routing"}, {"step": 6, "script": "form", "status": "completed", "description": "Generate form specifications and validation"}, {"step": 7, "script": "home", "status": "completed", "description": "Generate home page routing and card configurations"}, {"step": 8, "script": "notification", "status": "completed", "description": "Generate notification templates and configurations"}, {"step": 9, "script": "generateServer", "status": "completed", "description": "Generate server-side code based on specifications"}, {"step": 10, "script": "generateScreens", "status": "pending", "description": "Generate client-side screens and components"}]}